{"name": "My workflow 4", "nodes": [{"parameters": {"pollTimes": {"item": [{"mode": "everyMinute"}]}, "triggerOn": "specificFolder", "folderToWatch": {"__rl": true, "value": "https://drive.google.com/drive/u/2/folders/1LADP6VD4ojtFyjuTNiNwLS3jB25tXG8X", "mode": "url"}, "event": "fileUpdated", "options": {}}, "type": "n8n-nodes-base.googleDriveTrigger", "typeVersion": 1, "position": [-2120, 80], "id": "d52aa554-e386-4d85-8fbf-84e94421bf58", "name": "Google Drive Trigger", "credentials": {"googleDriveOAuth2Api": {"id": "wXPA0LnYGj7CEvSq", "name": "Google Drive account"}}}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $json.mimeType }}", "rightValue": "application/vnd.google-apps.document", "operator": {"type": "string", "operation": "equals"}, "id": "075a29bb-26e8-4d9d-8a3a-3718199ff9cc"}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "b5703c09-b09f-4fe5-a1c1-c82de4fe23c0", "leftValue": "={{ $json.mimeType }}", "rightValue": "application/vnd.google-apps.spreadsheet", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [-1980, 80], "id": "1edc89e3-8a38-47f2-a039-ec2995e37b0e", "name": "File Type Filter"}, {"parameters": {"url": "={{ `https://docs.google.com/document/d/${$json.id}/export?format=txt` }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "googleDriveOAuth2Api", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-1840, 20], "id": "e7eab45c-fe42-4c1e-96be-c136b237c52e", "name": "Export Google Docs as Text", "credentials": {"googleDriveOAuth2Api": {"id": "wXPA0LnYGj7CEvSq", "name": "Google Drive account"}}}, {"parameters": {"jsCode": "// Google Docs 內容提取\nconst item = $input.item;\n\nconsole.log('📄 Extract Docs Text:');\nconsole.log('  - Input item keys:', Object.keys(item));\n\nlet fileName = 'Unknown Document';\nlet fileId = 'unknown-id';\n\n// 從觸發器獲取文件信息\nif ($('Google Drive Trigger').length > 0) {\n  const triggerData = $('Google Drive Trigger').first().json;\n  fileName = triggerData.name || 'Unknown Document';\n  fileId = triggerData.id || 'unknown-id';\n  console.log('  - 從觸發器獲取文件名:', fileName);\n} else if ($('File Type Filter').length > 0) {\n  const filterData = $('File Type Filter').first().json;\n  fileName = filterData.name || 'Unknown Document';\n  fileId = filterData.id || 'unknown-id';\n  console.log('  - 從過濾器獲取文件名:', fileName);\n}\n\nlet extractedText = '';\n\nif (typeof item.json === 'string') {\n  extractedText = item.json;\n} else if (item.json && typeof item.json.data === 'string') {\n  extractedText = item.json.data;\n} else {\n  console.log('❌ 無法找到文本內容');\n  throw new Error(`Could not find text content in the response`);\n}\n\nif (!extractedText || extractedText.trim().length === 0) {\n  console.log('❌ 提取的文本為空');\n  throw new Error(`Extracted text from Google Doc is empty.`);\n}\n\nconst cleanedText = extractedText\n  .replace(/\\r\\n/g, ' ')\n  .replace(/\\n/g, ' ')\n  .replace(/\\s+/g, ' ')\n  .trim();\n\nconst result = {\n  text: cleanedText,\n  originalFileId: fileId,\n  originalFileName: fileName,\n  fileType: 'docs' // 明確設置為 docs\n};\n\nconsole.log('✅ Docs 文本提取完成:', {\n  fileType: result.fileType,\n  fileName: result.originalFileName,\n  textLength: result.text.length\n});\n\nreturn result;"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1700, 20], "id": "b78c9ad2-99c3-4e95-9f32-af09376d2d26", "name": "Extract Docs Text"}, {"parameters": {"url": "={{ `https://sheets.googleapis.com/v4/spreadsheets/${$json.id}/values/A:Z?valueRenderOption=UNFORMATTED_VALUE` }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "googleDriveOAuth2Api", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-1840, 180], "id": "ce63cfdc-2b92-4b59-9c15-8e8059d085b6", "name": "Read Sheet Data", "credentials": {"googleDriveOAuth2Api": {"id": "wXPA0LnYGj7CEvSq", "name": "Google Drive account"}}}, {"parameters": {"jsCode": "// 處理 Google Sheets 數據\nconst apiResponse = $json;\nlet combinedText = '';\nlet originalDataForRebuild = [];\n\nconsole.log('📊 Format Sheet Data:');\nconsole.log('  - API Response keys:', Object.keys(apiResponse));\n\nif (apiResponse.values && Array.isArray(apiResponse.values)) {\n  const rows = apiResponse.values;\n  console.log('  - 行數:', rows.length);\n  \n  rows.forEach((row, rowIndex) => {\n    originalDataForRebuild.push({\n      rowIndex: rowIndex,\n      values: row || []\n    });\n    \n    if (row && Array.isArray(row)) {\n      row.forEach(cell => {\n        const value = cell ? String(cell).trim() : '';\n        if (value) {\n          combinedText += value + '\\n';\n        }\n      });\n    }\n  });\n} else {\n  console.log('❌ 無效的 Google Sheets API 回應格式');\n  throw new Error('無效的 Google Sheets API 回應格式');\n}\n\nlet originalFileName = 'Unknown Sheet';\nlet fileId = 'unknown-id';\n\n// 從觸發器獲取文件信息\nif ($('Google Drive Trigger').length > 0) {\n  const triggerData = $('Google Drive Trigger').first().json;\n  originalFileName = triggerData.name || 'Unknown Sheet';\n  fileId = triggerData.id || 'unknown-id';\n  console.log('  - 從觸發器獲取文件名:', originalFileName);\n} else if ($('File Type Filter').length > 0) {\n  const filterData = $('File Type Filter').first().json;\n  originalFileName = filterData.name || 'Unknown Sheet';\n  fileId = filterData.id || 'unknown-id';\n  console.log('  - 從過濾器獲取文件名:', originalFileName);\n}\n\nconst result = {\n  text: combinedText.trim(),\n  originalFileId: fileId,\n  originalFileName: originalFileName,\n  fileType: 'sheets', // 明確設置為 sheets\n  originalData: originalDataForRebuild,\n  originalStructure: apiResponse.values\n};\n\nconsole.log('✅ Sheet 數據處理完成:', {\n  fileType: result.fileType,\n  fileName: result.originalFileName,\n  textLength: result.text.length,\n  rowCount: originalDataForRebuild.length\n});\n\nreturn result;"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1700, 180], "id": "6cc6c07b-851c-459d-9169-0a6b0fc6cafa", "name": "Format Sheet Data"}, {"parameters": {"jsCode": "// 智能合併邏輯 - 多重檢測文件類型\nconst allInputs = $input.all();\n\nconsole.log('🔄 Merge Extracted Content - 智能版本:');\nconsole.log('  - 輸入數量:', allInputs.length);\n\n// 詳細記錄所有可用的節點數據\nconsole.log('  - 可用節點:');\ntry {\n  console.log('    - Google Drive Trigger:', $('Google Drive Trigger').length > 0 ? '✅' : '❌');\n  console.log('    - File Type Filter:', $('File Type Filter').length > 0 ? '✅' : '❌');\n  console.log('    - Extract Docs Text:', $('Extract Docs Text').length > 0 ? '✅' : '❌');\n  console.log('    - Format Sheet Data:', $('Format Sheet Data').length > 0 ? '✅' : '❌');\n} catch (e) {\n  console.log('    - 檢查節點時出錯:', e.message);\n}\n\n// 方法1: 從觸發器獲取文件信息\nlet triggerData = null;\nlet originalMimeType = 'unknown';\nlet originalFileName = 'Unknown';\nlet originalFileId = 'unknown-id';\n\ntry {\n  if ($('Google Drive Trigger').length > 0) {\n    triggerData = $('Google Drive Trigger').first().json;\n    console.log('  - 觸發器完整數據:', JSON.stringify(triggerData, null, 2));\n    originalMimeType = triggerData.mimeType || 'unknown';\n    originalFileName = triggerData.name || 'Unknown';\n    originalFileId = triggerData.id || 'unknown-id';\n  }\n} catch (error) {\n  console.log('  - 獲取觸發器數據失敗:', error.message);\n}\n\n// 方法2: 從 File Type Filter 獲取文件信息\nif (originalMimeType === 'unknown') {\n  try {\n    if ($('File Type Filter').length > 0) {\n      const filterData = $('File Type Filter').first().json;\n      console.log('  - 過濾器完整數據:', JSON.stringify(filterData, null, 2));\n      originalMimeType = filterData.mimeType || 'unknown';\n      originalFileName = filterData.name || originalFileName;\n      originalFileId = filterData.id || originalFileId;\n    }\n  } catch (error) {\n    console.log('  - 獲取過濾器數據失敗:', error.message);\n  }\n}\n\nconsole.log('  - 文件信息匯總:', {\n  mimeType: originalMimeType,\n  fileName: originalFileName,\n  fileId: originalFileId\n});\n\n// 確定正確的文件類型\nlet correctFileType = 'unknown';\nif (originalMimeType === 'application/vnd.google-apps.spreadsheet') {\n  correctFileType = 'sheets';\n} else if (originalMimeType === 'application/vnd.google-apps.document') {\n  correctFileType = 'docs';\n}\n\n// 方法3: 如果仍然無法確定，從輸入數據推斷\nif (correctFileType === 'unknown') {\n  console.log('  - 嘗試從輸入數據推斷文件類型...');\n  for (let i = 0; i < allInputs.length; i++) {\n    const input = allInputs[i].json;\n    if (input && input.fileType) {\n      correctFileType = input.fileType;\n      console.log(`  - 從輸入 ${i} 推斷文件類型:`, correctFileType);\n      break;\n    }\n  }\n}\n\n// 方法4: 根據輸入結構推斷（最後手段）\nif (correctFileType === 'unknown') {\n  console.log('  - 根據輸入結構推斷文件類型...');\n  for (let i = 0; i < allInputs.length; i++) {\n    const input = allInputs[i].json;\n    if (input) {\n      // 如果有 originalStructure 或 originalData，很可能是 sheets\n      if (input.originalStructure || input.originalData) {\n        correctFileType = 'sheets';\n        console.log(`  - 從輸入 ${i} 的結構推斷為 sheets`);\n        break;\n      }\n      // 如果只有簡單文本，很可能是 docs\n      else if (input.text && !input.originalStructure && !input.originalData) {\n        correctFileType = 'docs';\n        console.log(`  - 從輸入 ${i} 的結構推斷為 docs`);\n        break;\n      }\n    }\n  }\n}\n\nconsole.log('  - 最終確定的文件類型:', correctFileType);\n\n// 處理輸入數據\nlet mergedData = {\n  text: '',\n  originalFileName: originalFileName,\n  fileType: correctFileType,\n  originalFileId: originalFileId,\n  originalData: null,\n  originalStructure: null\n};\n\n// 根據文件類型選擇正確的輸入\nif (correctFileType === 'sheets') {\n  // 對於 Sheets，優先使用 index 1 的輸入（來自 Format Sheet Data）\n  if (allInputs.length > 1 && allInputs[1] && allInputs[1].json) {\n    const sheetInput = allInputs[1].json;\n    console.log('  - 使用 Sheets 輸入 (index 1):', {\n      hasText: !!sheetInput.text,\n      textLength: sheetInput.text ? sheetInput.text.length : 0,\n      hasOriginalData: !!sheetInput.originalData,\n      hasOriginalStructure: !!sheetInput.originalStructure\n    });\n    \n    if (sheetInput.text) {\n      mergedData.text = sheetInput.text;\n      mergedData.originalData = sheetInput.originalData;\n      mergedData.originalStructure = sheetInput.originalStructure;\n    }\n  }\n} else if (correctFileType === 'docs') {\n  // 對於 Docs，優先使用 index 0 的輸入（來自 Extract Docs Text）\n  if (allInputs.length > 0 && allInputs[0] && allInputs[0].json) {\n    const docsInput = allInputs[0].json;\n    console.log('  - 使用 Docs 輸入 (index 0):', {\n      hasText: !!docsInput.text,\n      textLength: docsInput.text ? docsInput.text.length : 0\n    });\n    \n    if (docsInput.text) {\n      mergedData.text = docsInput.text;\n    }\n  }\n}\n\n// 如果仍然沒有文本，嘗試任何有效的輸入\nif (!mergedData.text) {\n  console.log('  - 嘗試查找任何有效輸入...');\n  for (let i = 0; i < allInputs.length; i++) {\n    const input = allInputs[i].json;\n    console.log(`  - 檢查輸入 ${i}:`, {\n      hasJson: !!input,\n      hasText: !!(input && input.text),\n      keys: input ? Object.keys(input) : []\n    });\n    \n    if (input && input.text && input.text.trim()) {\n      mergedData.text = input.text;\n      mergedData.originalData = input.originalData || null;\n      mergedData.originalStructure = input.originalStructure || null;\n      // 如果還沒確定文件類型，從這個輸入推斷\n      if (correctFileType === 'unknown' && input.fileType) {\n        mergedData.fileType = input.fileType;\n        correctFileType = input.fileType;\n      }\n      console.log(`  - 找到有效輸入 index ${i}:`, {\n        fileType: input.fileType,\n        textLength: input.text.length\n      });\n      break;\n    }\n  }\n}\n\n// 最終驗證\nif (!mergedData.text || mergedData.text.trim() === '') {\n  console.log('❌ 無法獲取文本內容');\n  console.log('  - 所有輸入詳情:', allInputs.map((input, i) => ({\n    index: i,\n    hasJson: !!input.json,\n    jsonKeys: input.json ? Object.keys(input.json) : [],\n    hasText: !!(input.json && input.json.text),\n    textPreview: input.json && input.json.text ? input.json.text.substring(0, 50) + '...' : 'N/A'\n  })));\n  throw new Error('無法從任何輸入中獲取有效的文本內容');\n}\n\n// 如果文件類型仍然未知，使用默認值\nif (mergedData.fileType === 'unknown') {\n  console.log('⚠️ 文件類型仍然未知，使用默認值 docs');\n  mergedData.fileType = 'docs';\n}\n\nconsole.log('✅ 合併完成:', {\n  fileType: mergedData.fileType,\n  fileName: mergedData.originalFileName,\n  textLength: mergedData.text.length,\n  hasOriginalData: !!mergedData.originalData,\n  hasOriginalStructure: !!mergedData.originalStructure\n});\n\nreturn mergedData;"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1480, 80], "id": "90c031ea-6db0-4f32-8691-20f6e7f80823", "name": "Merge Extracted Content"}, {"parameters": {"method": "POST", "url": "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=AIzaSyD5mbvRs_vIuAE9twS2wU-IpK_RMMHV9oo", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={{ JSON.stringify({\n  contents: [{\n    parts: [{\n      text: `You are a professional translation engine specializing in English to Traditional Chinese (Taiwan) translation. Your task:\n\n1. Translate the following text into Traditional Chinese suitable for Taiwan readers\n2. Preserve the EXACT line structure - if input has N lines, output must have N lines\n3. Use natural, fluent Chinese expressions while maintaining accuracy\n4. Keep professional terminology precise\n5. Do not add explanations or introductory text - only provide the translation\n\nText to translate:\n---\n${$json.text || 'No text content found'}`\n    }]\n  }]\n}) }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-1280, 80], "id": "401582aa-61eb-4f99-b130-0c176c934bc4", "name": "First Translation (<PERSON>)"}, {"parameters": {"jsCode": "// 處理 Gemini API 回應\nconst geminiResponse = $json;\n\nlet sourceData = { originalFileName: 'Unknown', fileType: 'unknown', originalData: null };\nif ($('Merge Extracted Content').length > 0) {\n  sourceData = $('Merge Extracted Content').first().json;\n}\n\nlet translatedText = 'Translation failed';\nif (geminiResponse.candidates && geminiResponse.candidates[0] && geminiResponse.candidates[0].content && geminiResponse.candidates[0].content.parts && geminiResponse.candidates[0].content.parts[0]) {\n  translatedText = geminiResponse.candidates[0].content.parts[0].text;\n}\n\nconsole.log('🔍 Process Gemini Response:');\nconsole.log('  - fileType:', sourceData.fileType);\nconsole.log('  - fileName:', sourceData.originalFileName);\nconsole.log('  - 翻譯長度:', translatedText ? translatedText.length : 0);\n\nreturn {\n  initialTranslation: translatedText,\n  originalFileName: sourceData.originalFileName,\n  fileType: sourceData.fileType,\n  originalData: sourceData.originalData,\n  originalStructure: sourceData.originalStructure\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1080, 80], "id": "d9be0cf4-47c2-4437-9147-bd34a8b57db4", "name": "Process Gemini Response"}, {"parameters": {"method": "POST", "url": "https://api.anthropic.com/v1/messages", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "x-api-key", "value": "************************************************************************************************************"}, {"name": "anthropic-version", "value": "2023-06-01"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={{ JSON.stringify({\n  \"model\": \"claude-3-5-sonnet-20240620\",\n  \"max_tokens\": 4096,\n  \"system\": \"你是一位資深的翻譯審校專家。你收到的內容是一段初步的中文翻譯。你的任務是進行以下改進：\\n1. **語意準確性**：確保翻譯完全傳達原文含義。\\n2. **語言自然性**：改善中文表達的流暢度和自然性，使其符合台灣讀者習慣。\\n\\n**最重要的規則是：** 輸入的文本可能包含多個由換行符分隔的獨立片段。你必須**嚴格保持原始的換行符結構**。如果輸入的某一行是空的，輸出的對應行也必須是空的。輸出的總行數必須與輸入的總行數完全相同。絕不添加任何額外的解釋或開場白，只需提供最終的、逐行對應的翻譯結果。\",\n  \"messages\": [\n    {\n      \"role\": \"user\",\n      \"content\": `初始中文翻譯：\\n---\\n${$json.initialTranslation}\\n---\\n\\n請提供改進後的翻譯版本。`\n    }\n  ]\n}) }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-880, 80], "id": "9e3aca75-f2a1-4c51-9847-8514a41760ae", "name": "Reflective Translation (<PERSON>)"}, {"parameters": {"jsCode": "// 解析 Claude API 回應\nconst claudeResponse = $json;\n\nlet upstreamData = { initialTranslation: 'No translation', originalFileName: 'Unknown', fileType: 'unknown', originalData: null, originalStructure: null };\n\nif ($('Process Gemini Response').length > 0) {\n  upstreamData = $('Process Gemini Response').first().json;\n}\n\nlet finalTranslation = upstreamData.initialTranslation;\nif (claudeResponse.content && claudeResponse.content[0] && claudeResponse.content[0].text) {\n  finalTranslation = claudeResponse.content[0].text;\n}\n\nconsole.log('🔍 Process Claude Response:');\nconsole.log('  - fileType:', upstreamData.fileType);\nconsole.log('  - fileName:', upstreamData.originalFileName);\nconsole.log('  - 最終翻譯長度:', finalTranslation ? finalTranslation.length : 0);\n\nreturn {\n  finalTranslation: finalTranslation.trim(),\n  originalFileName: upstreamData.originalFileName,\n  fileType: upstreamData.fileType,\n  originalData: upstreamData.originalData,\n  originalStructure: upstreamData.originalStructure\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-680, 80], "id": "3cf09bf7-85f3-4429-92fe-1ee7c8c9908e", "name": "Process Claude Response"}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $json.fileType }}", "rightValue": "docs", "operator": {"type": "string", "operation": "equals"}, "id": "docs-condition"}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "sheets-condition", "leftValue": "={{ $json.fileType }}", "rightValue": "sheets", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}]}, "options": {"fallbackOutput": 0}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [-480, 80], "id": "5eeb92e8-9a28-4796-b827-a553304f4aed", "name": "Output Type Switch"}, {"parameters": {"jsCode": "// 準備 Google Docs 內容 - 準備檔名API調用\nconst inputData = $json;\n\nconsole.log('🔍 Docs Filename Resolver - 被調用了！');\nconsole.log('  - 輸入數據:', JSON.stringify(inputData, null, 2));\n\nlet fileId = null;\ntry {\n  if ($('Google Drive Trigger').length > 0) {\n    const triggerData = $('Google Drive Trigger').first().json;\n    fileId = triggerData.id;\n    console.log('  - 從觸發器獲取檔案ID:', fileId);\n  }\n} catch (error) {\n  console.log('  - 獲取觸發器檔案ID失敗:', error.message);\n}\n\nif (!fileId) {\n  console.log('❌ 無法獲取檔案ID進行檔名解析');\n  throw new Error('無法獲取檔案ID進行檔名解析');\n}\n\nconsole.log('✅ Docs 路徑確認 - 這是正確的 Google Docs 處理流程');\n\n// 返回數據並準備API調用\nreturn {\n  ...inputData,\n  fileIdForAPI: fileId,\n  apiUrl: `https://www.googleapis.com/drive/v3/files/${fileId}?fields=name`\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-340, -60], "id": "66eb04af-2e77-4bb4-8b09-cbbfe2b0bd3d", "name": "Docs Filename Resolver"}, {"parameters": {"url": "={{ $json.apiUrl }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "googleDriveOAuth2Api", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-200, -60], "id": "974b97a1-6829-414b-9883-e40a2d925f06", "name": "Get Docs Filename API", "credentials": {"googleDriveOAuth2Api": {"id": "wXPA0LnYGj7CEvSq", "name": "Google Drive account"}}}, {"parameters": {"jsCode": "// 處理 Docs 檔名API響應並準備內容\nconst apiResponse = $json;\nconst docsData = $('Docs Filename Resolver').first().json;\n\nconsole.log('🎯 Process Docs Filename API Response:');\nconsole.log('  - API Response:', JSON.stringify(apiResponse, null, 2));\n\nlet finalFileName = 'DocsAPIFailed';\n\nif (apiResponse.name) {\n  finalFileName = apiResponse.name.replace(/\\.(doc|docx|txt|pdf)$/i, '');\n  console.log('✅ 從API獲取Docs檔名:', finalFileName);\n} else {\n  finalFileName = 'Document_' + new Date().toISOString().substring(0, 10);\n  console.log('❌ API失敗，使用備案檔名:', finalFileName);\n}\n\nconst finalTitle = finalFileName + '_翻譯版';\n\nconsole.log('🏆 最終Docs檔案標題:', finalTitle);\n\nreturn {\n  title: finalTitle,\n  content: docsData.finalTranslation,\n  finalFileName: finalFileName\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-60, -60], "id": "e5475fc1-2af3-48ac-a49a-686d47899ae9", "name": "Process Docs Filename Response"}, {"parameters": {"method": "POST", "url": "https://www.googleapis.com/drive/v3/files", "authentication": "predefinedCredentialType", "nodeCredentialType": "googleDriveOAuth2Api", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={{ JSON.stringify({\n  name: $json.title || 'Translated Document',\n  parents: ['1WJVpTCUhxUMfcsjGNs4vuLHwYaJ_NKGg'],\n  mimeType: 'application/vnd.google-apps.document'\n}) }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-200, 20], "id": "d875795b-e585-46a9-bf0d-0010f8d563e4", "name": "Create Google Doc via Drive API", "credentials": {"googleDriveOAuth2Api": {"id": "wXPA0LnYGj7CEvSq", "name": "Google Drive account"}}}, {"parameters": {"jsCode": "// Get document info and content\nconst docInfo = $json;\nconst documentId = docInfo.id;\n\nif (!documentId) {\n  throw new Error('Could not find document ID');\n}\n\nconst prepNodeItems = $('Process Docs Filename Response').all();\n\nif (!prepNodeItems || prepNodeItems.length === 0) {\n    throw new Error('Could not find output from the \"Process Docs Filename Response\" node.');\n}\n\nconst translationContent = prepNodeItems[0].json.content;\n\nif (!translationContent) {\n  throw new Error('Could not find \"content\" in the output of \"Process Docs Filename Response\" node.');\n}\n\nreturn {\n  documentId: documentId,\n  content: translationContent,\n  documentUrl: `https://docs.google.com/document/d/${documentId}/edit`\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-60, 20], "id": "087de0ff-2ea2-4531-8aef-8a3345f6413b", "name": "Prepare Final Payload"}, {"parameters": {"method": "POST", "url": "={{ `https://docs.googleapis.com/v1/documents/${$json.documentId}:batchUpdate` }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "googleDocsOAuth2Api", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={{ JSON.stringify({\n  requests: [\n    {\n      insertText: {\n        location: {\n          index: 1\n        },\n        text: $json.content || '無法獲取翻譯內容'\n      }\n    }\n  ]\n}) }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [80, 20], "id": "0ca3eb48-44de-4bde-bb44-c49e2f6efa06", "name": "Add Content to Google Doc", "credentials": {"googleDocsOAuth2Api": {"id": "aKNvhMNpGwKLTvmj", "name": "Google Docs account"}}}, {"parameters": {"jsCode": "// Rebuild Translated Sheet Data\nconsole.log('📊 Rebuild Translated Sheet Data - 被調用了！');\nconsole.log('❌ 錯誤：這個節點不應該被 Google Docs 調用');\n\nconst inputData = $json;\nconsole.log('  - 輸入數據:', JSON.stringify(inputData, null, 2));\n\nconst translatedText = inputData.finalTranslation;\nlet originalStructure = inputData.originalStructure;\n\nconsole.log('  - 翻譯文本長度:', translatedText ? translatedText.length : 0);\nconsole.log('  - 原始結構存在:', !!originalStructure);\n\nif (!originalStructure) {\n  console.log('originalStructure not found, trying other nodes...');\n  \n  if ($('Format Sheet Data').length > 0) {\n    const formatData = $('Format Sheet Data').first().json;\n    originalStructure = formatData.originalStructure;\n    console.log('  - 從 Format Sheet Data 獲取結構:', !!originalStructure);\n  }\n}\n\nif (!originalStructure) {\n  console.log('No originalStructure found, creating simple default structure...');\n  const lines = translatedText.split('\\n').filter(line => line.trim() !== '');\n  originalStructure = lines.map(line => [line]);\n  console.log('Created default structure with', originalStructure.length, 'rows');\n}\n\nconst translatedLines = translatedText.split('\\n').filter(line => line.trim() !== '');\nlet lineIndex = 0;\n\nconst rebuiltValues = [];\n\nfor (let rowIndex = 0; rowIndex < originalStructure.length; rowIndex++) {\n  const originalRow = originalStructure[rowIndex] || [];\n  const newRow = [];\n  \n  for (let colIndex = 0; colIndex < originalRow.length; colIndex++) {\n    if (lineIndex < translatedLines.length) {\n      newRow.push(translatedLines[lineIndex].trim());\n      lineIndex++;\n    } else {\n      newRow.push(originalRow[colIndex] || '');\n    }\n  }\n  \n  rebuiltValues.push(newRow);\n}\n\nconsole.log('⚠️ 警告：Google Docs 被錯誤地路由到 Sheets 處理流程');\nconsole.log('  - 重建的行數:', rebuiltValues.length);\n\nreturn {\n  values: rebuiltValues,\n  range: 'A:Z',\n  majorDimension: 'ROWS'\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-340, 200], "id": "75bb200f-11f5-4533-8872-41b11331de84", "name": "Rebuild Translated Sheet Data"}, {"parameters": {"jsCode": "// 獲取檔案ID用於實時檔名解析\nconst inputData = $json;\nconst fileId = $('Google Drive Trigger').first().json.id;\n\nconsole.log('🔍 Real-time Filename Resolver:');\nconsole.log('  - 檔案ID:', fileId);\n\nif (!fileId) {\n  throw new Error('無法獲取檔案ID進行檔名解析');\n}\n\n// 返回數據並準備API調用\nreturn {\n  ...inputData,\n  fileIdForAPI: fileId,\n  apiUrl: `https://www.googleapis.com/drive/v3/files/${fileId}?fields=name`\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-200, 200], "id": "76b86f7f-2d52-4119-ac4c-9e7cd957492d", "name": "Filename Resolver"}, {"parameters": {"url": "={{ $json.apiUrl }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "googleDriveOAuth2Api", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-60, 200], "id": "363ae2ec-56dc-47ef-b36e-041be3aa0405", "name": "Get Filename API", "credentials": {"googleDriveOAuth2Api": {"id": "wXPA0LnYGj7CEvSq", "name": "Google Drive account"}}}, {"parameters": {"jsCode": "// 處理檔名API響應並創建最終標題\nconst apiResponse = $json;\nconst rebuiltData = $('Rebuild Translated Sheet Data').first().json;\n\nconsole.log('🎯 Process Filename API Response:');\nconsole.log('  - API Response:', JSON.stringify(apiResponse, null, 2));\n\nlet finalFileName = 'APIFailed';\n\nif (apiResponse.name) {\n  finalFileName = apiResponse.name.replace(/\\.(xlsx?|csv|ods|gsheet)$/i, '');\n  console.log('✅ 從API獲取檔名:', finalFileName);\n} else {\n  finalFileName = 'Sheet_' + new Date().toISOString().substring(0, 10);\n  console.log('❌ API失敗，使用備案檔名:', finalFileName);\n}\n\nconst finalTitle = finalFileName + '_翻譯版';\n\nconsole.log('🏆 最終檔案標題:', finalTitle);\n\nreturn {\n  ...rebuiltData,\n  finalSheetTitle: finalTitle,\n  finalFileName: finalFileName\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [80, 200], "id": "dc6b4e1d-f9ab-429e-8ced-a8ebc198838c", "name": "Process Filename Response"}, {"parameters": {"method": "POST", "url": "https://sheets.googleapis.com/v4/spreadsheets", "authentication": "predefinedCredentialType", "nodeCredentialType": "googleDriveOAuth2Api", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={{ JSON.stringify({\n  properties: {\n    title: $json.finalSheetTitle || 'Fallback_翻譯版'\n  },\n  sheets: [{\n    properties: {\n      title: '翻譯結果',\n      gridProperties: {\n        rowCount: 1000,\n        columnCount: 26\n      }\n    }\n  }]\n}) }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [220, 200], "id": "a744e52d-c14d-4791-ba75-d9a8c9039132", "name": "Create Translated Sheet", "credentials": {"googleDriveOAuth2Api": {"id": "wXPA0LnYGj7CEvSq", "name": "Google Drive account"}}}, {"parameters": {"jsCode": "// 準備Sheet內容寫入\nconst createResponse = $json;\nconst processedData = $('Process Filename Response').first().json;\n\nconst newSpreadsheetId = createResponse.spreadsheetId;\n\nif (!newSpreadsheetId) {\n  throw new Error(\"Could not get new spreadsheet ID from create response.\");\n}\n\nconst valuesToWrite = processedData.values || [];\n\nreturn {\n  spreadsheetId: newSpreadsheetId,\n  values: valuesToWrite,\n  range: '翻譯結果!A:Z',\n  majorDimension: 'ROWS',\n  newFileUrl: `https://docs.google.com/spreadsheets/d/${newSpreadsheetId}/edit`\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [360, 200], "id": "c3c42543-e6c7-47be-8a84-94d0e3c6c156", "name": "Prepare Sheet Write"}, {"parameters": {"method": "PATCH", "url": "={{ `https://www.googleapis.com/drive/v3/files/${$json.spreadsheetId}` }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "googleDriveOAuth2Api", "sendQuery": true, "queryParameters": {"parameters": [{"name": "addParents", "value": "1WJVpTCUhxUMfcsjGNs4vuLHwYaJ_NKGg"}, {"name": "fields", "value": "id,parents"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [500, 200], "id": "6e4f9c3d-cd67-4dd3-ae67-977b2ed51755", "name": "Move to Output Folder", "credentials": {"googleDriveOAuth2Api": {"id": "wXPA0LnYGj7CEvSq", "name": "Google Drive account"}}}, {"parameters": {"jsCode": "// 重建寫入Payload\nconst moveResponse = $json;\nconst prepareData = $('Prepare Sheet Write').first().json;\n\nreturn {\n  spreadsheetId: prepareData.spreadsheetId,\n  values: prepareData.values,\n  range: prepareData.range,\n  majorDimension: prepareData.majorDimension,\n  newFileUrl: prepareData.newFileUrl\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [640, 200], "id": "3f780b38-3d09-41d3-8271-71cd4ff09a61", "name": "Rebuild Write Payload"}, {"parameters": {"method": "PUT", "url": "={{ `https://sheets.googleapis.com/v4/spreadsheets/${$json.spreadsheetId}/values/${encodeURIComponent($json.range)}?valueInputOption=RAW` }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "googleDriveOAuth2Api", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={{ JSON.stringify({\n  values: $json.values,\n  majorDimension: 'ROWS'\n}) }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [780, 200], "id": "a5d0c754-c5e6-4a34-824f-7e98d4e6ba7e", "name": "Write to Sheet", "credentials": {"googleDriveOAuth2Api": {"id": "wXPA0LnYGj7CEvSq", "name": "Google Drive account"}}}], "pinData": {}, "connections": {"Google Drive Trigger": {"main": [[{"node": "File Type Filter", "type": "main", "index": 0}]]}, "File Type Filter": {"main": [[{"node": "Export Google Docs as Text", "type": "main", "index": 0}], [{"node": "Read Sheet Data", "type": "main", "index": 0}]]}, "Export Google Docs as Text": {"main": [[{"node": "Extract Docs Text", "type": "main", "index": 0}]]}, "Extract Docs Text": {"main": [[{"node": "Merge Extracted Content", "type": "main", "index": 0}]]}, "Read Sheet Data": {"main": [[{"node": "Format Sheet Data", "type": "main", "index": 0}]]}, "Merge Extracted Content": {"main": [[{"node": "First Translation (<PERSON>)", "type": "main", "index": 0}]]}, "First Translation (Gemini)": {"main": [[{"node": "Process Gemini Response", "type": "main", "index": 0}]]}, "Process Gemini Response": {"main": [[{"node": "Reflective Translation (<PERSON>)", "type": "main", "index": 0}]]}, "Reflective Translation (Claude)": {"main": [[{"node": "Process Claude Response", "type": "main", "index": 0}]]}, "Process Claude Response": {"main": [[{"node": "Output Type Switch", "type": "main", "index": 0}]]}, "Output Type Switch": {"main": [[{"node": "Docs Filename Resolver", "type": "main", "index": 0}], [{"node": "Rebuild Translated Sheet Data", "type": "main", "index": 0}]]}, "Docs Filename Resolver": {"main": [[{"node": "Get Docs Filename API", "type": "main", "index": 0}]]}, "Get Docs Filename API": {"main": [[{"node": "Process Docs Filename Response", "type": "main", "index": 0}]]}, "Process Docs Filename Response": {"main": [[{"node": "Create Google Doc via Drive API", "type": "main", "index": 0}]]}, "Create Google Doc via Drive API": {"main": [[{"node": "Prepare Final Payload", "type": "main", "index": 0}]]}, "Prepare Final Payload": {"main": [[{"node": "Add Content to Google Doc", "type": "main", "index": 0}]]}, "Rebuild Translated Sheet Data": {"main": [[{"node": "Filename Resolver", "type": "main", "index": 0}]]}, "Filename Resolver": {"main": [[{"node": "Get Filename API", "type": "main", "index": 0}]]}, "Get Filename API": {"main": [[{"node": "Process Filename Response", "type": "main", "index": 0}]]}, "Process Filename Response": {"main": [[{"node": "Create Translated Sheet", "type": "main", "index": 0}]]}, "Create Translated Sheet": {"main": [[{"node": "Prepare Sheet Write", "type": "main", "index": 0}]]}, "Prepare Sheet Write": {"main": [[{"node": "Move to Output Folder", "type": "main", "index": 0}]]}, "Move to Output Folder": {"main": [[{"node": "Rebuild Write Payload", "type": "main", "index": 0}]]}, "Rebuild Write Payload": {"main": [[{"node": "Write to Sheet", "type": "main", "index": 0}]]}, "Format Sheet Data": {"main": [[{"node": "Merge Extracted Content", "type": "main", "index": 1}]]}}, "active": false, "settings": {"executionOrder": "v1", "callerPolicy": "workflowsFromSameOwner"}, "versionId": "b11fad4f-c36e-47f8-ad51-6697d4b3769b", "meta": {"templateCredsSetupCompleted": true, "instanceId": "cf8945d612793a3345978938d5d97bfc51695b35f0132e97cf82bfc6ce55e338"}, "id": "xTkG5CxPFcjTgxio", "tags": []}
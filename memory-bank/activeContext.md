# 當前工作背景 - N8N 翻譯工作流程

## 專案當前狀態
### 完成的工作
1. **工作流程設計完成**：已建立完整的 N8N 工作流程
2. **核心功能實現**：所有主要翻譯功能節點已配置
3. **API 整合完成**：Google 服務和 AI 翻譯引擎已整合
4. **記憶庫建立**：已建立完整的專案文檔結構

### 工作流程節點配置
**已配置的 16 個節點：**
1. `Google Drive Trigger` - 監控文件變更
2. `File Type Filter` - 文件類型分流
3. `Download Google Docs as Text` - Docs 文件下載
4. `Download Google Sheets as Excel` - Sheets 文件下載
5. `Extract Docs Text` - 提取 Docs 文字內容
6. `Parse Sheets Content` - 解析 Sheets 內容
7. `Extract Sheets Text` - 提取 Sheets 文字
8. `Load Translation Dictionary` - 載入翻譯字典
9. `Preserve Text` - 整合文字內容
10. `Parse Excel Dictionary` - 解析字典內容
11. `First Translation (Gemini EN→ZH)` - Gemini 初譯
12. `Apply Translation Dictionary` - 應用字典校正
13. `Reflective Translation (Claude)` - Claude 反思改進
14. `Parse Claude Response` - 解析 Claude 回應
15. `Output Type Switch` - 輸出類型分流
16. 輸出處理節點群組

## 當前技術重點

### 反思式翻譯方法論
**三階段翻譯流程：**
1. **初始翻譯** (Gemini Pro)
   - 英文到繁體中文的基礎翻譯
   - 針對台灣讀者優化的提示詞
   - 保持原文語意和語調

2. **字典校正** (JavaScript)
   - 載入預定義的術語對照表
   - 自動替換專業術語翻譯
   - 確保術語翻譯的一致性

3. **反思改進** (Claude 3.5 Sonnet)
   - 語意準確性檢查
   - 語言自然性改善
   - 用詞精準性優化
   - 語境適配性調整

### 關鍵實現模式
1. **資料保持策略**：原始文件結構信息全程保留
2. **條件路由**：根據文件類型選擇不同處理路徑
3. **錯誤恢復**：中間資料保存支援流程恢復
4. **格式重建**：翻譯後重建原始文件格式

## 當前配置重點

### API 整合配置
- **Google Drive 監控**：每分鐘輪詢特定資料夾
- **認證管理**：OAuth2 統一認證機制
- **字典來源**：Google Sheets 格式的翻譯對照表
- **輸出管理**：自動檔名處理和資料夾歸檔

### 品質控制機制
- **雙重引擎驗證**：Gemini + Claude 組合確保品質
- **術語一致性**：外部字典確保專業術語統一
- **格式完整性**：保持原始文件的結構和佈局
- **可追溯性**：完整的處理鏈路可回溯

## 近期工作重點

### 部署準備
1. **認證配置**：設置所有必要的 API 認證
2. **測試驗證**：完整流程的端到端測試
3. **性能調優**：優化 API 調用頻率和錯誤處理
4. **監控設置**：建立工作流程運行監控

### 功能擴展考量
1. **支援更多文件格式**：PDF、Word 等格式支援
2. **多語言支援**：擴展到其他語言對
3. **批量處理優化**：提升大量文件的處理效率
4. **品質評估機制**：加入翻譯品質自動評估

## 技術決策記錄

### 已確定的技術選擇
1. **N8N 作為工作流程引擎**：低代碼、視覺化、易維護
2. **雙 AI 引擎架構**：Gemini 初譯 + Claude 改進
3. **Google Drive 整合**：原生支援、無縫整合
4. **反思式翻譯方法**：多層次品質保證機制

### 待決定的技術議題
1. **部署策略**：本地 vs 雲端部署選擇
2. **擴容方案**：高負載情況下的處理策略
3. **備份機制**：工作流程配置和資料備份方案
4. **版本控制**：工作流程變更的版本管理